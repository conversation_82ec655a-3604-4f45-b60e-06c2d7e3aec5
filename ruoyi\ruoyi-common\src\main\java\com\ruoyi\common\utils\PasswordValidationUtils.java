package com.ruoyi.common.utils;

import static org.gzgdata.GzgWeakPasswordCheck.getGzgWeakPasswordReasons;

/**
 * 密码强度校验工具类
 * 集成广州港弱密码检测器
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
public class PasswordValidationUtils {

    /**
     * 密码强度足够的标识
     */
    private static final String PASSWORD_STRONG_ENOUGH = "密码强度足够";

    /**
     * 校验密码强度
     * 
     * @param password 密码
     * @param username 用户名（可选，用于检测密码是否包含用户名）
     * @return 校验结果，如果密码强度足够返回null，否则返回具体的错误原因
     */
    public static String validatePassword(String password, String username) {
        if (password == null || password.trim().isEmpty()) {
            return "密码不能为空";
        }
        
        if (username == null) {
            username = "";
        }
        
        try {
            // 调用广州港弱密码检测器
            String weakReason = getGzgWeakPasswordReasons(password, username);
            
            // 如果密码强度足够，返回null表示校验通过
            if (PASSWORD_STRONG_ENOUGH.equals(weakReason)) {
                return null;
            } else {
                // 返回具体的错误原因
                return weakReason;
            }
        } catch (Exception e) {
            // 如果弱密码检测器出现异常，回退到基础校验
            return validatePasswordBasic(password);
        }
    }

    /**
     * 校验密码强度（不检查用户名）
     * 
     * @param password 密码
     * @return 校验结果，如果密码强度足够返回null，否则返回具体的错误原因
     */
    public static String validatePassword(String password) {
        return validatePassword(password, "");
    }

    /**
     * 基础密码校验（备用方案）
     * 当广州港弱密码检测器不可用时使用
     * 
     * @param password 密码
     * @return 校验结果
     */
    private static String validatePasswordBasic(String password) {
        if (password == null || password.length() < 8 || password.length() > 32) {
            return "密码长度必须在8到32个字符之间";
        }
        
        // 检查是否包含大小写字母、数字、特殊字符中的至少三种
        boolean hasLower = password.matches(".*[a-z].*");
        boolean hasUpper = password.matches(".*[A-Z].*");
        boolean hasDigit = password.matches(".*[0-9].*");
        boolean hasSpecial = password.matches(".*[\\W_].*");
        
        int typeCount = 0;
        if (hasLower) typeCount++;
        if (hasUpper) typeCount++;
        if (hasDigit) typeCount++;
        if (hasSpecial) typeCount++;
        
        if (typeCount < 3) {
            return "密码至少需要包含数字、小写字母、大写字母、特殊字符中的三项";
        }
        
        return null;
    }

    /**
     * 检查密码强度是否足够
     * 
     * @param password 密码
     * @param username 用户名
     * @return true表示密码强度足够，false表示不足够
     */
    public static boolean isPasswordStrong(String password, String username) {
        return validatePassword(password, username) == null;
    }

    /**
     * 检查密码强度是否足够（不检查用户名）
     * 
     * @param password 密码
     * @return true表示密码强度足够，false表示不足够
     */
    public static boolean isPasswordStrong(String password) {
        return validatePassword(password) == null;
    }
}
